oa-prod$ npm test -- --testPathPattern="CleanupCoordinatorEnhanced.test.ts" --verbose

> oa-framework@1.0.0 test
> jest --testPathPattern=CleanupCoordinatorEnhanced.test.ts --verbose

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts (39.711 s, 429 MB heap size)
  CleanupCoordinatorEnhanced
    Cleanup Templates System
      ✓ should register and validate cleanup templates (11 ms)
      ✓ should validate template structure and detect issues (33 ms)
      ✓ should execute templates with dependency resolution (5 ms)
      ✓ should filter templates by criteria (3 ms)
      ✓ should track template execution metrics (6 ms)
    Advanced Dependency Resolution
      ✓ should create dependency graph without hanging (3 ms)
      ✓ should detect circular dependencies (6 ms)
      ✓ should build and analyze dependency graphs (4 ms)
      ✓ should optimize operation execution order (4 ms)
      ✓ should throw error for circular dependencies in optimization (13 ms)
      ✓ should identify bottlenecks and optimization opportunities (3 ms)
    Rollback and Recovery System
      ✓ should create and manage checkpoints (5 ms)
      ✓ should rollback to checkpoint successfully (3 ms)
      ✓ should rollback operation using most recent checkpoint (6 ms)
      ✓ should validate rollback capability (8 ms)
      ✓ should filter checkpoints by criteria (3 ms)
      ✓ should cleanup old checkpoints (3 ms)
      ✓ should handle rollback failures gracefully (3 ms)
    Modular Architecture Integration
      ✓ should properly initialize all 15 extracted modules (4 ms)
      ✓ should coordinate operations across modules with <5ms overhead (2 ms)
      ✕ should handle module-level errors without cascading failures (4 ms)
    Resilient Timing Integration
      ✓ should use resilient timing for all coordination operations (2 ms)
      ✓ should record timing metrics for coordination overhead (2 ms)
      ✕ should handle timing reliability issues gracefully (5004 ms)
    ES6+ Modernization Validation
      ✓ should execute modernized async/await patterns correctly (3 ms)
      ✕ should maintain identical error handling behavior (5 ms)
    Performance Requirements
      ✓ should maintain <5ms coordination overhead under load (3 ms)
      ✕ should handle 1000+ concurrent operations efficiently (30006 ms)
    Integration and Performance
      ✓ should maintain backward compatibility with base CleanupCoordinator (8 ms)
      ✓ should handle template execution within performance requirements (6 ms)
      ✓ should handle dependency analysis within performance requirements (8 ms)
      ✓ should handle checkpoint creation within performance requirements (8 ms)
    Factory Functions
      ✓ should create enhanced cleanup coordinator via factory function (3 ms)
      ✓ should get enhanced cleanup coordinator via getter function (3 ms)
    Error Handling and Edge Cases
      ✓ should handle template execution with non-existent template (4 ms)
      ✓ should handle rollback with non-existent checkpoint (15 ms)
      ✓ should handle rollback with no checkpoints for operation (4 ms)
      ✓ should handle disabled rollback system (5 ms)
      ✓ should handle empty template operations (4 ms)
      ✓ should handle malformed component patterns (2 ms)

  ● CleanupCoordinatorEnhanced › Modular Architecture Integration › should handle module-level errors without cascading failures

    expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      1097 |       // Verify coordinator is still functional after error
      1098 |       const healthStatus = await coordinator.getHealthStatus();
    > 1099 |       expect(healthStatus.operational).toBe(true);
           |                                        ^
      1100 |
      1101 |       // Verify other modules are still functional
      1102 |       const moduleStatus = await coordinator.getModuleStatus();

      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:1099:40)

  ● CleanupCoordinatorEnhanced › Resilient Timing Integration › should handle timing reliability issues gracefully

    thrown: "Exceeded timeout of 5000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      1193 |     });
      1194 |
    > 1195 |     it('should handle timing reliability issues gracefully', async () => {
           |     ^
      1196 |       // ✅ ENHANCED TIMING RELIABILITY: Test with controlled fallback scenarios
      1197 |       // Avoid mocking process.hrtime which causes infinite loops in Jest
      1198 |

      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:1195:5
      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:1111:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:34:1)

  ● CleanupCoordinatorEnhanced › ES6+ Modernization Validation › should maintain identical error handling behavior

    expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      1302 |       // ✅ ENHANCED COORDINATOR RESILIENCE: Verify coordinator remains operational after all error tests
      1303 |       const healthStatus = await coordinator.getHealthStatus();
    > 1304 |       expect(healthStatus.operational).toBe(true);
           |                                        ^
      1305 |
      1306 |       // ✅ ENHANCED ERROR METRICS: Verify error handling metrics are maintained
      1307 |       const metrics = coordinator.getMetrics();

      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:1304:40)

  ● CleanupCoordinatorEnhanced › Performance Requirements › should handle 1000+ concurrent operations efficiently

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      1346 |     });
      1347 |
    > 1348 |     it('should handle 1000+ concurrent operations efficiently', async () => {
           |     ^
      1349 |       // Scalability Test: High concurrency scenarios
      1350 |
      1351 |       const concurrentOperations = 500; // Reduced for reliable test execution

      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:1348:5
      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:1314:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:34:1)

Test Suites: 1 failed, 1 total
Tests:       4 failed, 36 passed, 40 total
Snapshots:   0 total
Time:        39.972 s
Ran all test suites matching /CleanupCoordinatorEnhanced.test.ts/i.
