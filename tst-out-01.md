oa-prod$ npm test -- shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts

> oa-framework@1.0.0 test
> jest shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts (34.026 s, 425 MB heap size)
  EventHandlerRegistryEnhanced
    Backward Compatibility
      ✓ should maintain all base class functionality (11 ms)
      ✓ should preserve base class metrics functionality (3 ms)
      ✓ should handle handler unregistration like base class (3 ms)
    Event Emission System
      ✓ should emit events to all registered handlers (5 ms)
      ✓ should handle handler errors gracefully (2 ms)
      ✓ should emit events to specific clients (3 ms)
      ✓ should process event batches correctly (10 ms)
      ✕ should handle event emission timeout (30007 ms)
      ✓ should meet performance requirements for emission (7 ms)
    Handler Middleware System
      ✓ should execute middleware in priority order (6 ms)
      ✓ should skip handler when middleware returns false (6 ms)
      ✕ should handle errors through middleware (6 ms)
      ✓ should execute after-handler middleware (3 ms)
      ✓ should remove middleware correctly (3 ms)
      ✓ should meet middleware performance requirements (4 ms)
    Advanced Handler Deduplication
      ✕ should detect duplicate handlers by reference (6 ms)
      ✕ should detect duplicate handlers by signature (5 ms)
      ✕ should use custom deduplication function (4 ms)
      ✕ should merge metadata on duplicate detection (5 ms)
      ✕ should meet deduplication performance requirements (6 ms)
    Event Buffering and Queuing
      ✕ should buffer events and flush periodically (42 ms)
      ✕ should auto-flush when threshold is reached (3 ms)
      ✕ should handle buffer overflow correctly (5 ms)
      ✕ should process events with priority strategy (3 ms)
      ✕ should meet buffer operation performance requirements (3 ms)
    Enhanced Metrics and Monitoring
      ✓ should provide enhanced metrics (5 ms)
      ✓ should track middleware execution metrics (2 ms)

  ● EventHandlerRegistryEnhanced › Event Emission System › should handle event emission timeout

    thrown: "Exceeded timeout of 30000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      211 |     });
      212 |
    > 213 |     it('should handle event emission timeout', async () => {
          |     ^
      214 |       // ✅ GOVERNANCE COMPLIANCE: Test Jest mock-aware timeout behavior with enhanced validation
      215 |
      216 |       let handlerStarted = false;

      at shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:213:5
      at shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:125:3
      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:22:1)

  ● EventHandlerRegistryEnhanced › Handler Middleware System › should handle errors through middleware

    expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      346 |       const result = await registry.emitEvent('test-event', {});
      347 |
    > 348 |       expect(errorHandled).toBe(true);
          |                            ^
      349 |       expect(result.successfulHandlers).toBe(1); // Error was handled
      350 |       expect(result.failedHandlers).toBe(0);
      351 |     });

      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:348:28)

  ● EventHandlerRegistryEnhanced › Advanced Handler Deduplication › should detect duplicate handlers by reference

    expect(received).toBe(expected) // Object.is equality

    Expected: "client1:test-event:1754005584335:it3q59"
    Received: "client1:test-event:1754005584335:uf05mh"

      430 |       const id2 = await registry.registerHandler('client1', 'test-event', callback);
      431 |
    > 432 |       expect(id1).toBe(id2); // Should return same handler ID
          |                   ^
      433 |       expect(registry.getHandlersForEvent('test-event')).toHaveLength(1);
      434 |     });
      435 |

      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:432:19)

  ● EventHandlerRegistryEnhanced › Advanced Handler Deduplication › should detect duplicate handlers by signature

    expect(received).toBe(expected) // Object.is equality

    Expected: "client1:test-event:1754005584341:1hfsd5"
    Received: "client1:test-event:1754005584341:sq6vtj"

      450 |       const id2 = await registry.registerHandler('client1', 'test-event', callback2);
      451 |
    > 452 |       expect(id1).toBe(id2);
          |                   ^
      453 |       expect(registry.getHandlersForEvent('test-event')).toHaveLength(1);
      454 |     });
      455 |

      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:452:19)

  ● EventHandlerRegistryEnhanced › Advanced Handler Deduplication › should use custom deduplication function

    expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      476 |       const id2 = await registry.registerHandler('client1', 'test-event', callback2);
      477 |
    > 478 |       expect(customFunctionCalled).toBe(true);
          |                                    ^
      479 |       expect(id1).toBe(id2);
      480 |     });
      481 |

      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:478:36)

  ● EventHandlerRegistryEnhanced › Advanced Handler Deduplication › should merge metadata on duplicate detection

    expect(received).toBe(expected) // Object.is equality

    Expected: "client1:test-event:1754005584350:rt4w7o"
    Received: "client1:test-event:1754005584350:b9v4nl"

      491 |       });
      492 |
    > 493 |       expect(id1).toBe(id2);
          |                   ^
      494 |
      495 |       const handler = registry.getHandler(id1);
      496 |       expect(handler?.metadata).toEqual({

      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:493:19)

  ● EventHandlerRegistryEnhanced › Advanced Handler Deduplication › should meet deduplication performance requirements

    expect(received).toHaveLength(expected)

    Expected length: 1
    Received length: 10
    Received array:  [{"callback": [Function callback], "clientId": "client1", "eventType": "test-event", "id": "client1:test-event:1754005584355:ju0db3", "lastUsed": 2025-07-31T23:46:24.355Z, "metadata": undefined, "registeredAt": 2025-07-31T23:46:24.355Z}, {"callback": [Function callback], "clientId": "client1", "eventType": "test-event", "id": "client1:test-event:1754005584355:5mbqms", "lastUsed": 2025-07-31T23:46:24.355Z, "metadata": undefined, "registeredAt": 2025-07-31T23:46:24.355Z}, {"callback": [Function callback], "clientId": "client1", "eventType": "test-event", "id": "client1:test-event:1754005584355:xlcr78", "lastUsed": 2025-07-31T23:46:24.355Z, "metadata": undefined, "registeredAt": 2025-07-31T23:46:24.355Z}, {"callback": [Function callback], "clientId": "client1", "eventType": "test-event", "id": "client1:test-event:1754005584355:p7jk9z", "lastUsed": 2025-07-31T23:46:24.355Z, "metadata": undefined, "registeredAt": 2025-07-31T23:46:24.355Z}, {"callback": [Function callback], "clientId": "client1", "eventType": "test-event", "id": "client1:test-event:1754005584355:yz9kmt", "lastUsed": 2025-07-31T23:46:24.355Z, "metadata": undefined, "registeredAt": 2025-07-31T23:46:24.355Z}, {"callback": [Function callback], "clientId": "client1", "eventType": "test-event", "id": "client1:test-event:1754005584355:u7ddkb", "lastUsed": 2025-07-31T23:46:24.355Z, "metadata": undefined, "registeredAt": 2025-07-31T23:46:24.355Z}, {"callback": [Function callback], "clientId": "client1", "eventType": "test-event", "id": "client1:test-event:1754005584355:qvwrc2", "lastUsed": 2025-07-31T23:46:24.355Z, "metadata": undefined, "registeredAt": 2025-07-31T23:46:24.355Z}, {"callback": [Function callback], "clientId": "client1", "eventType": "test-event", "id": "client1:test-event:1754005584355:0zst7t", "lastUsed": 2025-07-31T23:46:24.355Z, "metadata": undefined, "registeredAt": 2025-07-31T23:46:24.355Z}, {"callback": [Function callback], "clientId": "client1", "eventType": "test-event", "id": "client1:test-event:1754005584355:0wdfvb", "lastUsed": 2025-07-31T23:46:24.355Z, "metadata": undefined, "registeredAt": 2025-07-31T23:46:24.355Z}, {"callback": [Function callback], "clientId": "client1", "eventType": "test-event", "id": "client1:test-event:1754005584355:gfi3pi", "lastUsed": 2025-07-31T23:46:24.355Z, "metadata": undefined, "registeredAt": 2025-07-31T23:46:24.355Z}]

      515 |       // Performance requirement: <1ms per handler registration deduplication check
      516 |       expect(avgDuration).toBeLessThan(1);
    > 517 |       expect(registry.getHandlersForEvent('test-event')).toHaveLength(1);
          |                                                          ^
      518 |     });
      519 |   });
      520 |

      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:517:58)

  ● EventHandlerRegistryEnhanced › Event Buffering and Queuing › should buffer events and flush periodically

    Unhandled error. (TypeError: Cannot read properties of undefined (reading 'start')

      363 |     options: IConfigMergeOptions = { overrideDefaults: true, validateAfterMerge: false, preserveExistingValues: false }
      364 |   ): IEnhancedBufferConfig {
    > 365 |     const mergeContext = this._resilientTimer.start();
          |                                               ^
      366 |
      367 |     try {
      368 |       const merged: IEnhancedBufferConfig = {

      at BufferConfigurationManager._mergeConfigurations (shared/src/base/atomic-circular-buffer-enhanced/modules/BufferConfigurationManager.ts:365:47)
      at new BufferConfigurationManager (shared/src/base/atomic-circular-buffer-enhanced/modules/BufferConfigurationManager.ts:161:32)
      at new AtomicCircularBufferEnhanced (shared/src/base/AtomicCircularBufferEnhanced.ts:180:34)
      at EventBuffering.doInitialize (shared/src/base/event-handler-registry/modules/EventBuffering.ts:123:25)
      at EventBuffering.initialize (shared/src/base/MemorySafeResourceManager.ts:168:18)
      at EventHandlerRegistryEnhanced.doInitialize (shared/src/base/EventHandlerRegistryEnhanced.ts:242:43)
      at EventHandlerRegistryEnhanced.initialize (shared/src/base/MemorySafeResourceManager.ts:168:7)
      at EventHandlerRegistryEnhanced.initialize (shared/src/base/EventHandlerRegistryEnhanced.ts:197:7)
      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:538:7))
      at EventBuffering.initialize (shared/src/base/MemorySafeResourceManager.ts:174:12)
      at EventHandlerRegistryEnhanced.doInitialize (shared/src/base/EventHandlerRegistryEnhanced.ts:242:7)
      at EventHandlerRegistryEnhanced.initialize (shared/src/base/MemorySafeResourceManager.ts:168:7)
      at EventHandlerRegistryEnhanced.initialize (shared/src/base/EventHandlerRegistryEnhanced.ts:197:7)
      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:538:7)

  ● EventHandlerRegistryEnhanced › Event Buffering and Queuing › should auto-flush when threshold is reached

    Unhandled error. (TypeError: Cannot read properties of undefined (reading 'start')

      363 |     options: IConfigMergeOptions = { overrideDefaults: true, validateAfterMerge: false, preserveExistingValues: false }
      364 |   ): IEnhancedBufferConfig {
    > 365 |     const mergeContext = this._resilientTimer.start();
          |                                               ^
      366 |
      367 |     try {
      368 |       const merged: IEnhancedBufferConfig = {

      at BufferConfigurationManager._mergeConfigurations (shared/src/base/atomic-circular-buffer-enhanced/modules/BufferConfigurationManager.ts:365:47)
      at new BufferConfigurationManager (shared/src/base/atomic-circular-buffer-enhanced/modules/BufferConfigurationManager.ts:161:32)
      at new AtomicCircularBufferEnhanced (shared/src/base/AtomicCircularBufferEnhanced.ts:180:34)
      at EventBuffering.doInitialize (shared/src/base/event-handler-registry/modules/EventBuffering.ts:123:25)
      at EventBuffering.initialize (shared/src/base/MemorySafeResourceManager.ts:168:18)
      at EventHandlerRegistryEnhanced.doInitialize (shared/src/base/EventHandlerRegistryEnhanced.ts:242:43)
      at EventHandlerRegistryEnhanced.initialize (shared/src/base/MemorySafeResourceManager.ts:168:7)
      at EventHandlerRegistryEnhanced.initialize (shared/src/base/EventHandlerRegistryEnhanced.ts:197:7)
      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:538:7))
      at EventBuffering.initialize (shared/src/base/MemorySafeResourceManager.ts:174:12)
      at EventHandlerRegistryEnhanced.doInitialize (shared/src/base/EventHandlerRegistryEnhanced.ts:242:7)
      at EventHandlerRegistryEnhanced.initialize (shared/src/base/MemorySafeResourceManager.ts:168:7)
      at EventHandlerRegistryEnhanced.initialize (shared/src/base/EventHandlerRegistryEnhanced.ts:197:7)
      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:538:7)

  ● EventHandlerRegistryEnhanced › Event Buffering and Queuing › should handle buffer overflow correctly

    Unhandled error. (TypeError: Cannot read properties of undefined (reading 'start')

      363 |     options: IConfigMergeOptions = { overrideDefaults: true, validateAfterMerge: false, preserveExistingValues: false }
      364 |   ): IEnhancedBufferConfig {
    > 365 |     const mergeContext = this._resilientTimer.start();
          |                                               ^
      366 |
      367 |     try {
      368 |       const merged: IEnhancedBufferConfig = {

      at BufferConfigurationManager._mergeConfigurations (shared/src/base/atomic-circular-buffer-enhanced/modules/BufferConfigurationManager.ts:365:47)
      at new BufferConfigurationManager (shared/src/base/atomic-circular-buffer-enhanced/modules/BufferConfigurationManager.ts:161:32)
      at new AtomicCircularBufferEnhanced (shared/src/base/AtomicCircularBufferEnhanced.ts:180:34)
      at EventBuffering.doInitialize (shared/src/base/event-handler-registry/modules/EventBuffering.ts:123:25)
      at EventBuffering.initialize (shared/src/base/MemorySafeResourceManager.ts:168:18)
      at EventHandlerRegistryEnhanced.doInitialize (shared/src/base/EventHandlerRegistryEnhanced.ts:242:43)
      at EventHandlerRegistryEnhanced.initialize (shared/src/base/MemorySafeResourceManager.ts:168:7)
      at EventHandlerRegistryEnhanced.initialize (shared/src/base/EventHandlerRegistryEnhanced.ts:197:7)
      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:538:7))
      at EventBuffering.initialize (shared/src/base/MemorySafeResourceManager.ts:174:12)
      at EventHandlerRegistryEnhanced.doInitialize (shared/src/base/EventHandlerRegistryEnhanced.ts:242:7)
      at EventHandlerRegistryEnhanced.initialize (shared/src/base/MemorySafeResourceManager.ts:168:7)
      at EventHandlerRegistryEnhanced.initialize (shared/src/base/EventHandlerRegistryEnhanced.ts:197:7)
      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:538:7)

  ● EventHandlerRegistryEnhanced › Event Buffering and Queuing › should process events with priority strategy

    Unhandled error. (TypeError: Cannot read properties of undefined (reading 'start')

      363 |     options: IConfigMergeOptions = { overrideDefaults: true, validateAfterMerge: false, preserveExistingValues: false }
      364 |   ): IEnhancedBufferConfig {
    > 365 |     const mergeContext = this._resilientTimer.start();
          |                                               ^
      366 |
      367 |     try {
      368 |       const merged: IEnhancedBufferConfig = {

      at BufferConfigurationManager._mergeConfigurations (shared/src/base/atomic-circular-buffer-enhanced/modules/BufferConfigurationManager.ts:365:47)
      at new BufferConfigurationManager (shared/src/base/atomic-circular-buffer-enhanced/modules/BufferConfigurationManager.ts:161:32)
      at new AtomicCircularBufferEnhanced (shared/src/base/AtomicCircularBufferEnhanced.ts:180:34)
      at EventBuffering.doInitialize (shared/src/base/event-handler-registry/modules/EventBuffering.ts:123:25)
      at EventBuffering.initialize (shared/src/base/MemorySafeResourceManager.ts:168:18)
      at EventHandlerRegistryEnhanced.doInitialize (shared/src/base/EventHandlerRegistryEnhanced.ts:242:43)
      at EventHandlerRegistryEnhanced.initialize (shared/src/base/MemorySafeResourceManager.ts:168:7)
      at EventHandlerRegistryEnhanced.initialize (shared/src/base/EventHandlerRegistryEnhanced.ts:197:7)
      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:538:7))
      at EventBuffering.initialize (shared/src/base/MemorySafeResourceManager.ts:174:12)
      at EventHandlerRegistryEnhanced.doInitialize (shared/src/base/EventHandlerRegistryEnhanced.ts:242:7)
      at EventHandlerRegistryEnhanced.initialize (shared/src/base/MemorySafeResourceManager.ts:168:7)
      at EventHandlerRegistryEnhanced.initialize (shared/src/base/EventHandlerRegistryEnhanced.ts:197:7)
      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:538:7)

  ● EventHandlerRegistryEnhanced › Event Buffering and Queuing › should meet buffer operation performance requirements

    Unhandled error. (TypeError: Cannot read properties of undefined (reading 'start')

      363 |     options: IConfigMergeOptions = { overrideDefaults: true, validateAfterMerge: false, preserveExistingValues: false }
      364 |   ): IEnhancedBufferConfig {
    > 365 |     const mergeContext = this._resilientTimer.start();
          |                                               ^
      366 |
      367 |     try {
      368 |       const merged: IEnhancedBufferConfig = {

      at BufferConfigurationManager._mergeConfigurations (shared/src/base/atomic-circular-buffer-enhanced/modules/BufferConfigurationManager.ts:365:47)
      at new BufferConfigurationManager (shared/src/base/atomic-circular-buffer-enhanced/modules/BufferConfigurationManager.ts:161:32)
      at new AtomicCircularBufferEnhanced (shared/src/base/AtomicCircularBufferEnhanced.ts:180:34)
      at EventBuffering.doInitialize (shared/src/base/event-handler-registry/modules/EventBuffering.ts:123:25)
      at EventBuffering.initialize (shared/src/base/MemorySafeResourceManager.ts:168:18)
      at EventHandlerRegistryEnhanced.doInitialize (shared/src/base/EventHandlerRegistryEnhanced.ts:242:43)
      at EventHandlerRegistryEnhanced.initialize (shared/src/base/MemorySafeResourceManager.ts:168:7)
      at EventHandlerRegistryEnhanced.initialize (shared/src/base/EventHandlerRegistryEnhanced.ts:197:7)
      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:538:7))
      at EventBuffering.initialize (shared/src/base/MemorySafeResourceManager.ts:174:12)
      at EventHandlerRegistryEnhanced.doInitialize (shared/src/base/EventHandlerRegistryEnhanced.ts:242:7)
      at EventHandlerRegistryEnhanced.initialize (shared/src/base/MemorySafeResourceManager.ts:168:7)
      at EventHandlerRegistryEnhanced.initialize (shared/src/base/EventHandlerRegistryEnhanced.ts:197:7)
      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:538:7)

Test Suites: 1 failed, 1 total
Tests:       12 failed, 15 passed, 27 total
Snapshots:   0 total
Time:        34.287 s
Ran all test suites matching /shared\/src\/base\/__tests__\/EventHandlerRegistryEnhanced.test.ts/i.
