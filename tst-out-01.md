oa-prod$ npm test -- __tests__/EventHandlerRegistryEnhanced.test.ts --verbose

> oa-framework@1.0.0 test
> jest __tests__/EventHandlerRegistryEnhanced.test.ts --verbose

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts (442 MB heap size)
  EventHandlerRegistryEnhanced
    Backward Compatibility
      ✓ should maintain all base class functionality (7 ms)
      ✓ should preserve base class metrics functionality (3 ms)
      ✓ should handle handler unregistration like base class (3 ms)
    Event Emission System
      ✓ should emit events to all registered handlers (5 ms)
      ✓ should handle handler errors gracefully (3 ms)
      ✓ should emit events to specific clients (4 ms)
      ✓ should process event batches correctly (3 ms)
      ✓ should handle event emission timeout (4 ms)
      ✓ should meet performance requirements for emission (5 ms)
    Handler Middleware System
      ✓ should execute middleware in priority order (3 ms)
      ✓ should skip handler when middleware returns false (2 ms)
      ✕ should handle errors through middleware (3 ms)
      ✓ should execute after-handler middleware (2 ms)
      ✓ should remove middleware correctly (2 ms)
      ✓ should meet middleware performance requirements (6 ms)
    Advanced Handler Deduplication
      ✓ should detect duplicate handlers by reference (3 ms)
      ✓ should detect duplicate handlers by signature (2 ms)
      ✓ should use custom deduplication function (2 ms)
      ✓ should merge metadata on duplicate detection (2 ms)
      ✓ should meet deduplication performance requirements (3 ms)
    Event Buffering and Queuing
      ✕ should buffer events and flush periodically (5 ms)
      ✕ should auto-flush when threshold is reached (3 ms)
      ✕ should handle buffer overflow correctly (4 ms)
      ✕ should process events with priority strategy (4 ms)
      ✓ should meet buffer operation performance requirements (6 ms)
    Enhanced Metrics and Monitoring
      ✓ should provide enhanced metrics (2 ms)
      ✓ should track middleware execution metrics (2 ms)

  ● EventHandlerRegistryEnhanced › Handler Middleware System › should handle errors through middleware

    expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      346 |       const result = await registry.emitEvent('test-event', {});
      347 |
    > 348 |       expect(errorHandled).toBe(true);
          |                            ^
      349 |       expect(result.successfulHandlers).toBe(1); // Error was handled
      350 |       expect(result.failedHandlers).toBe(0);
      351 |     });

      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:348:28)

  ● EventHandlerRegistryEnhanced › Event Buffering and Queuing › should buffer events and flush periodically

    expect(received).toBe(expected) // Object.is equality

    Expected: 2
    Received: 0

      558 |
      559 |       // Now handlers should be called
    > 560 |       expect(handlerCallCount).toBe(2);
          |                                ^
      561 |     });
      562 |
      563 |     it('should auto-flush when threshold is reached', async () => {

      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:560:32)

  ● EventHandlerRegistryEnhanced › Event Buffering and Queuing › should auto-flush when threshold is reached

    expect(received).toBe(expected) // Object.is equality

    Expected: 3
    Received: 0

      575 |
      576 |       // The auto-flush should have triggered automatically
    > 577 |       expect(handlerCallCount).toBe(3);
          |                                ^
      578 |     });
      579 |
      580 |     it('should handle buffer overflow correctly', async () => {

      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:577:32)

  ● EventHandlerRegistryEnhanced › Event Buffering and Queuing › should handle buffer overflow correctly

    expect(received).toBe(expected) // Object.is equality

    Expected: 2
    Received: 0

      622 |
      623 |       // Should process remaining events (event1 dropped, event2 and event3 processed)
    > 624 |       expect(overflowHandlerCallCount).toBe(2);
          |                                        ^
      625 |
      626 |       await overflowRegistry.shutdown();
      627 |     });

      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:624:40)

  ● EventHandlerRegistryEnhanced › Event Buffering and Queuing › should process events with priority strategy

    expect(received).toHaveLength(expected)

    Expected length: 3
    Received length: 0
    Received array:  []

      657 |       await priorityRegistry.flushBufferedEvents();
      658 |
    > 659 |       expect(processedEvents).toHaveLength(3);
          |                               ^
      660 |       // Events should be processed in priority order (critical, high, low)
      661 |       expect(processedEvents[0].id).toBe(3); // critical
      662 |       expect(processedEvents[1].id).toBe(2); // high

      at Object.<anonymous> (shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts:659:31)

Test Suites: 1 failed, 1 total
Tests:       5 failed, 22 passed, 27 total
Snapshots:   0 total
Time:        4.428 s
Ran all test suites matching /__tests__\/EventHandlerRegistryEnhanced.test.ts/i.
