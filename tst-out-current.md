
> oa-framework@1.0.0 test
> jest --testPathPattern=shared/src/base/__tests__/modules/cleanup --silent

PASS shared/src/base/__tests__/modules/cleanup/TemplateWorkflows.test.ts (234 MB heap size)
  TemplateWorkflows
    Basic Workflow Execution
      ✓ should execute simple workflow successfully (8 ms)
      ✓ should handle empty workflow gracefully (3 ms)
      ✓ should update execution metrics during workflow (2 ms)
      ✓ should generate unique execution IDs (2 ms)
    Parallel & Sequential Execution
      ✓ should execute parallel workflow with independent steps (7 ms)
      ✓ should execute sequential workflow with dependencies (3 ms)
      ✓ should respect workflow configuration settings (2 ms)
      ✓ should allow configuration updates (2 ms)
    Retry Logic & Error Handling
      ✓ should retry failed steps according to configuration (5 ms)
      ✓ should handle component registry failures gracefully (2 ms)
      ✓ should execute dry run mode without actual operations (3 ms)
      ✓ should skip conditions when specified in options (3 ms)
    Component Integration & Simulation
      ✓ should integrate with component registry for component discovery (2 ms)
      ✓ should simulate different operation types correctly (3 ms)
      ✓ should handle component overrides in execution options (6 ms)
    Utility Functions & Performance
      ✓ should create workflow executor with factory function (2 ms)
      ✓ should execute workflow using utility function (3 ms)
      ✓ should maintain performance requirements for complex workflows (3 ms)

PASS shared/src/base/__tests__/modules/cleanup/CleanupTemplateManager.test.ts (241 MB heap size)
  CleanupTemplateManager
    Template Registration
      ✓ should register valid template successfully (5 ms)
      ✓ should validate template structure during registration (21 ms)
      ✓ should detect circular dependencies in template operations (4 ms)
      ✓ should initialize template metrics upon registration (2 ms)
    Template Execution
      ✓ should execute template with target components (5 ms)
      ✓ should handle template execution errors gracefully (5 ms)
      ✓ should generate unique execution IDs (14 ms)
      ✓ should reject execution of non-existent template (4 ms)
    Template Metrics
      ✓ should collect execution metrics during template execution (3 ms)
      ✓ should return empty metrics for non-existent template (2 ms)
      ✓ should return all template metrics when no template ID specified (4 ms)
    Module Integration
      ✓ should integrate with TemplateValidator for validation (2 ms)
      ✓ should integrate with TemplateWorkflowExecutor for execution (3 ms)
      ✓ should integrate with DependencyGraph for dependency management (1 ms)
    Performance & Memory Safety
      ✓ should maintain performance requirements during template operations (1 ms)
      ✓ should properly cleanup resources during shutdown (6 ms)

PASS shared/src/base/__tests__/modules/cleanup/TemplateDependencies.test.ts (256 MB heap size)
  TemplateDependencies
    Basic Graph Operations
      ✓ should add nodes to the graph (2 ms)
      ✓ should add edges between nodes (2 ms)
      ✓ should add dependencies correctly (2 ms)
      ✓ should handle duplicate node additions gracefully (1 ms)
      ✓ should clear the graph completely (5 ms)
    Cycle Detection
      ✓ should detect no cycles in acyclic graph (1 ms)
      ✓ should detect simple two-node cycle (2 ms)
      ✓ should detect complex multi-node cycle (2 ms)
      ✓ should find all cycles in graph (2 ms)
      ✓ should handle self-referencing nodes (1 ms)
    Topological Sorting
      ✓ should perform topological sort on simple DAG (2 ms)
      ✓ should handle complex dependency relationships (3 ms)
      ✓ should return empty array for cyclic graph (2 ms)
      ✓ should handle disconnected components (3 ms)
    Critical Path & Parallel Analysis
      ✓ should calculate critical path correctly (2 ms)
      ✓ should identify parallel execution groups (4 ms)
      ✓ should calculate graph metrics accurately (2 ms)
      ✓ should detect transitive dependencies (1 ms)
    Utility Functions & Performance
      ✓ should create dependency graph from operations (2 ms)
      ✓ should validate dependency graph for issues (2 ms)
      ✓ should detect validation issues in problematic graphs (2 ms)
      ✓ should clone dependency graph accurately (1 ms)
      ✓ should maintain performance requirements for complex graphs (2 ms)
      ✓ should handle edge cases gracefully (3 ms)

PASS shared/src/base/__tests__/modules/cleanup/TemplateValidation.test.ts (262 MB heap size)
  TemplateValidation
    Template Structure Validation
      ✓ should validate correct template structure (3 ms)
      ✓ should reject template with missing ID (2 ms)
      ✓ should reject template with no operations (2 ms)
      ✓ should warn about missing description (2 ms)
      ✓ should validate operation structure completeness (2 ms)
    Dependency Validation
      ✓ should validate correct dependency relationships (2 ms)
      ✓ should detect invalid dependency references (1 ms)
      ✓ should detect circular dependencies (2 ms)
    Condition Evaluation
      ✓ should evaluate always condition correctly (4 ms)
      ✓ should evaluate on_success condition correctly (2 ms)
      ✓ should evaluate on_failure condition correctly (3 ms)
      ✓ should evaluate component_exists condition correctly (2 ms)
      ✓ should validate custom condition requirements (2 ms)
    Quality Scoring & Utilities
      ✓ should calculate quality score based on template completeness (1 ms)
      ✓ should penalize quality score for validation issues (2 ms)
      ✓ should match components using regex patterns (1 ms)
      ✓ should handle invalid regex patterns gracefully (2 ms)
      ✓ should use validateTemplate utility function (1 ms)
    Performance & Extended Validation
      ✓ should maintain performance requirements during validation (1 ms)
      ✓ should provide comprehensive extended validation results (3 ms)

PASS shared/src/base/__tests__/modules/cleanup/PerformanceValidation.test.ts (256 MB heap size)
  Performance Validation Tests
    Template Execution Performance
      ✓ should execute template within 100ms requirement (4 ms)
      ✓ should maintain performance with multiple concurrent executions (4 ms)
      ✓ should validate memory overhead stays within 2% limit (7 ms)
    System Performance Metrics
      ✓ should validate Jest compatibility performance (7 ms)
      ✓ should validate batched async yield performance (2 ms)
    Performance Regression Detection
      ✓ should detect performance regressions in template execution (4 ms)

Test Suites: 5 passed, 5 total
Tests:       84 passed, 84 total
Snapshots:   0 total
Time:        2.74 s, estimated 3 s
