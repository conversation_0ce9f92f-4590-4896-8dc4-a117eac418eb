/**
 * @file BufferConfigurationManager
 * @filepath shared/src/base/atomic-circular-buffer-enhanced/modules/BufferConfigurationManager.ts
 * @task-id M-TSK-01.SUB-01.2.ENH-01.MOD-05
 * @component buffer-configuration-manager
 * @reference foundation-context.MEMORY-SAFETY.007.MOD-05
 * @template enhanced-memory-safe-module
 * @tier T0
 * @context foundation-context
 * @category Memory-Safety-Enhanced-Module
 * @created 2025-07-29 09:00:00 +03
 * @modified 2025-07-29 09:00:00 +03
 *
 * @description
 * Enhanced buffer configuration management module providing:
 * - Comprehensive configuration validation and normalization
 * - Default configuration management with environment-aware settings
 * - Configuration merging and inheritance patterns
 * - Type-safe configuration interfaces and validation rules
 * - Performance-optimized configuration operations
 * - Integration with all buffer modules for consistent configuration
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * @task-compliance M-TSK-01.SUB-01.2.ENH-01.MOD-05
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/base/MemorySafeResourceManager
 * @depends-on shared/src/base/utils/ResilientTiming
 * @depends-on shared/src/base/utils/ResilientMetrics
 * @integrates-with shared/src/base/AtomicCircularBufferEnhanced
 * @integrates-with BufferStrategyManager
 * @integrates-with BufferPersistenceManager
 * @enables enhanced-configuration-management
 * @enables configuration-validation
 * @related-contexts foundation-context, memory-safety-context, enhancement-context
 * @governance-impact framework-foundation, enhanced-atomic-operations
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type memory-safety-module-enhanced
 * @lifecycle-stage implementation
 * @testing-status comprehensive-test-coverage
 * @deployment-ready true
 * @monitoring-enabled true
 * @enhancement-phase phase-1
 * @backward-compatibility 100%
 * @performance-requirements <5ms-configuration-operations
 *
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   enhancement-validated: true
 *   anti-simplification-compliant: true
 *
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-07-29) - Initial modular extraction from AtomicCircularBufferEnhanced.ts
 * v1.0.0 (2025-07-29) - Added comprehensive configuration validation and management
 * v1.0.0 (2025-07-29) - Implemented type-safe configuration interfaces and defaults
 */

import { MemorySafeResourceManager } from '../../MemorySafeResourceManager';
import { SimpleLogger, ILoggingService } from '../../LoggingMixin';
import { ResilientTimer } from '../../utils/ResilientTiming';
import { ResilientMetricsCollector } from '../../utils/ResilientMetrics';
import { IBufferStrategy } from './BufferStrategyManager';
import { IPersistenceConfig } from './BufferPersistenceManager';

// ============================================================================
// SECTION 1: CONFIGURATION INTERFACES & TYPES (Lines 1-100)
// AI Context: "Enhanced configuration interfaces and validation types"
// ============================================================================

/**
 * Enhanced buffer configuration interface
 */
export interface IEnhancedBufferConfig {
  // Core buffer settings
  maxSize: number;
  initialCapacity?: number;
  
  // Strategy configuration
  strategy: IBufferStrategy;
  
  // Persistence configuration
  persistence?: IPersistenceConfig;
  
  // Performance settings
  performance: {
    enableMetrics: boolean;
    metricsRetentionMs: number;
    optimizationInterval: number;
    maxAccessHistorySize: number;
  };
  
  // Monitoring settings
  monitoring: {
    enableDetailedLogging: boolean;
    logLevel: 'debug' | 'info' | 'warn' | 'error';
    enablePerformanceTracking: boolean;
    alertThresholds: {
      hitRateBelow: number;
      averageAccessTimeAbove: number;
      fragmentationAbove: number;
    };
  };
}

/**
 * Configuration validation result
 */
export interface IConfigValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
  normalizedConfig: IEnhancedBufferConfig;
}

/**
 * Configuration merge options
 */
export interface IConfigMergeOptions {
  overrideDefaults: boolean;
  validateAfterMerge: boolean;
  preserveExistingValues: boolean;
}

// ============================================================================
// SECTION 2: BUFFER CONFIGURATION MANAGER CLASS (Lines 101-180)
// AI Context: "Main configuration manager class with validation and defaults"
// ============================================================================

/**
 * Enhanced buffer configuration manager with comprehensive validation
 * 
 * Provides enterprise-grade configuration management with:
 * - Type-safe configuration validation and normalization
 * - Environment-aware default configuration management
 * - Configuration merging and inheritance patterns
 * - Performance-optimized configuration operations
 */
export class BufferConfigurationManager extends MemorySafeResourceManager implements ILoggingService {
  private _logger: SimpleLogger;
  private _resilientTimer!: ResilientTimer;
  private _metricsCollector!: ResilientMetricsCollector;
  
  // Configuration state
  private _currentConfig: IEnhancedBufferConfig;
  private _defaultConfig: IEnhancedBufferConfig;

  constructor(initialConfig?: Partial<IEnhancedBufferConfig>) {
    super();
    this._logger = new SimpleLogger('BufferConfigurationManager');
    this._defaultConfig = this._createDefaultConfig();
    this._currentConfig = this._mergeConfigurations(this._defaultConfig, initialConfig || {});
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Initialize configuration manager
   */
  protected async doInitialize(): Promise<void> {
    // ✅ RESILIENT TIMING: Initialize timing infrastructure
    this._resilientTimer = new ResilientTimer({
      enableFallbacks: true,
      maxExpectedDuration: 5000, // 5 seconds max for configuration operations
      unreliableThreshold: 3,
      estimateBaseline: 5 // 5ms baseline for configuration operations
    });

    this._metricsCollector = new ResilientMetricsCollector({
      enableFallbacks: true,
      cacheUnreliableValues: false,
      maxMetricsAge: 300000, // 5 minutes
      defaultEstimates: new Map([
        ['configValidation', 5],
        ['configMerging', 3],
        ['configNormalization', 2]
      ])
    });

    this.logInfo('BufferConfigurationManager initialized');
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Shutdown configuration manager
   */
  protected async doShutdown(): Promise<void> {
    this.logInfo('BufferConfigurationManager shutdown completed');
  }

  // ============================================================================
  // SECTION 3: CONFIGURATION VALIDATION (Lines 181-250)
  // AI Context: "Configuration validation and normalization methods"
  // ============================================================================

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Validate configuration
   */
  public validateConfiguration(config: Partial<IEnhancedBufferConfig>): IConfigValidationResult {
    const validationContext = this._resilientTimer.start();
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Validate maxSize
      if (config.maxSize !== undefined) {
        if (config.maxSize <= 0) {
          errors.push('maxSize must be greater than 0');
        }
        if (config.maxSize > 1000000) {
          warnings.push('maxSize is very large, consider performance implications');
        }
      }

      // Validate initialCapacity
      if (config.initialCapacity !== undefined && config.maxSize !== undefined) {
        if (config.initialCapacity > config.maxSize) {
          errors.push('initialCapacity cannot be greater than maxSize');
        }
      }

      // Validate strategy
      if (config.strategy) {
        const strategyValidation = this._validateStrategy(config.strategy);
        errors.push(...strategyValidation.errors);
        warnings.push(...strategyValidation.warnings);
      }

      // Validate performance settings
      if (config.performance) {
        if (config.performance.metricsRetentionMs !== undefined && config.performance.metricsRetentionMs < 60000) {
          warnings.push('metricsRetentionMs is very low, may impact analytics quality');
        }
        if (config.performance.maxAccessHistorySize !== undefined && config.performance.maxAccessHistorySize > 10000) {
          warnings.push('maxAccessHistorySize is very high, may impact memory usage');
        }
      }

      // Create normalized configuration
      const normalizedConfig = this._mergeConfigurations(this._defaultConfig, config);

      const timingResult = validationContext.end();
      this._metricsCollector.recordTiming('configValidation', timingResult);

      const result: IConfigValidationResult = {
        valid: errors.length === 0,
        errors,
        warnings,
        normalizedConfig
      };

      this.logDebug('Configuration validation completed', {
        valid: result.valid,
        errorCount: errors.length,
        warningCount: warnings.length,
        operationTime: timingResult.duration
      });

      return result;

    } catch (error) {
      validationContext.end();
      this.logError('Configuration validation failed', error);
      throw error;
    }
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Update configuration
   */
  public updateConfiguration(newConfig: Partial<IEnhancedBufferConfig>): IConfigValidationResult {
    const validation = this.validateConfiguration(newConfig);
    
    if (validation.valid) {
      this._currentConfig = validation.normalizedConfig;
      this.logInfo('Configuration updated successfully', {
        hasWarnings: validation.warnings.length > 0,
        warningCount: validation.warnings.length
      });
    } else {
      this.logError('Configuration update failed', new Error(validation.errors.join(', ')));
    }

    return validation;
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Get current configuration
   */
  public getCurrentConfiguration(): IEnhancedBufferConfig {
    return { ...this._currentConfig };
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Get default configuration
   */
  public getDefaultConfiguration(): IEnhancedBufferConfig {
    return { ...this._defaultConfig };
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Reset to default configuration
   */
  public resetToDefaults(): void {
    this._currentConfig = { ...this._defaultConfig };
    this.logInfo('Configuration reset to defaults');
  }

  // ============================================================================
  // SECTION 4: CONFIGURATION UTILITIES (Lines 251-350)
  // AI Context: "Configuration merging, defaults, and utility methods"
  // ============================================================================

  /**
   * Create default configuration
   */
  private _createDefaultConfig(): IEnhancedBufferConfig {
    return {
      maxSize: 1000,
      initialCapacity: 100,
      strategy: {
        evictionPolicy: 'lru',
        compactionThreshold: 0.3,
        autoCompaction: true
      },
      persistence: {
        enabled: false,
        snapshotInterval: 300000, // 5 minutes
        maxSnapshots: 10,
        storageProvider: 'memory'
      },
      performance: {
        enableMetrics: true,
        metricsRetentionMs: 3600000, // 1 hour
        optimizationInterval: 600000, // 10 minutes
        maxAccessHistorySize: 1000
      },
      monitoring: {
        enableDetailedLogging: process.env.NODE_ENV === 'development',
        logLevel: process.env.NODE_ENV === 'production' ? 'warn' : 'debug',
        enablePerformanceTracking: true,
        alertThresholds: {
          hitRateBelow: 50,
          averageAccessTimeAbove: 10,
          fragmentationAbove: 40
        }
      }
    };
  }

  /**
   * Merge configurations with proper precedence
   */
  private _mergeConfigurations(
    baseConfig: IEnhancedBufferConfig,
    overrideConfig: Partial<IEnhancedBufferConfig>,
    options: IConfigMergeOptions = { overrideDefaults: true, validateAfterMerge: false, preserveExistingValues: false }
  ): IEnhancedBufferConfig {
    const mergeContext = this._resilientTimer.start();

    try {
      const merged: IEnhancedBufferConfig = {
        maxSize: overrideConfig.maxSize ?? baseConfig.maxSize,
        initialCapacity: overrideConfig.initialCapacity ?? baseConfig.initialCapacity,
        strategy: {
          ...baseConfig.strategy,
          ...overrideConfig.strategy
        },
        persistence: overrideConfig.persistence ? {
          ...baseConfig.persistence,
          ...overrideConfig.persistence
        } : baseConfig.persistence,
        performance: {
          ...baseConfig.performance,
          ...overrideConfig.performance
        },
        monitoring: {
          ...baseConfig.monitoring,
          ...overrideConfig.monitoring,
          alertThresholds: {
            ...baseConfig.monitoring.alertThresholds,
            ...overrideConfig.monitoring?.alertThresholds
          }
        }
      };

      const timingResult = mergeContext.end();
      this._metricsCollector.recordTiming('configMerging', timingResult);

      return merged;

    } catch (error) {
      mergeContext.end();
      this.logError('Configuration merging failed', error);
      throw error;
    }
  }

  /**
   * Validate strategy configuration
   */
  private _validateStrategy(strategy: Partial<IBufferStrategy>): { errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    const validPolicies = ['lru', 'lfu', 'random', 'fifo', 'custom'];
    if (strategy.evictionPolicy && !validPolicies.includes(strategy.evictionPolicy)) {
      errors.push(`Invalid eviction policy: ${strategy.evictionPolicy}`);
    }

    if (strategy.evictionPolicy === 'custom' && !strategy.customEvictionFn) {
      errors.push('Custom eviction policy requires customEvictionFn');
    }

    if (strategy.compactionThreshold !== undefined) {
      if (strategy.compactionThreshold < 0 || strategy.compactionThreshold > 1) {
        errors.push('compactionThreshold must be between 0 and 1');
      }
      if (strategy.compactionThreshold > 0.8) {
        warnings.push('High compaction threshold may impact performance');
      }
    }

    return { errors, warnings };
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Export configuration for external use
   */
  public exportConfiguration(): string {
    try {
      return JSON.stringify(this._currentConfig, null, 2);
    } catch (error) {
      this.logError('Failed to export configuration', error);
      throw error;
    }
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Import configuration from JSON
   */
  public importConfiguration(configJson: string): IConfigValidationResult {
    try {
      const parsedConfig = JSON.parse(configJson) as Partial<IEnhancedBufferConfig>;
      return this.updateConfiguration(parsedConfig);
    } catch (error) {
      this.logError('Failed to import configuration', error);
      throw new Error('Invalid configuration JSON format');
    }
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Get configuration summary
   */
  public getConfigurationSummary(): Record<string, any> {
    return {
      maxSize: this._currentConfig.maxSize,
      evictionPolicy: this._currentConfig.strategy.evictionPolicy,
      persistenceEnabled: this._currentConfig.persistence?.enabled || false,
      metricsEnabled: this._currentConfig.performance.enableMetrics,
      loggingLevel: this._currentConfig.monitoring.logLevel
    };
  }

  // Logging interface implementation
  logInfo(message: string, details?: Record<string, unknown>): void {
    this._logger.logInfo(message, details);
  }

  logError(message: string, error: unknown, details?: Record<string, unknown>): void {
    this._logger.logError(message, error, details);
  }

  logDebug(message: string, details?: Record<string, unknown>): void {
    this._logger.logDebug(message, details);
  }

  logWarning(message: string, details?: Record<string, unknown>): void {
    this._logger.logWarning(message, details);
  }
}
