/**
 * @file Cleanup Utilities
 * @filepath shared/src/base/modules/cleanup/CleanupUtilities.ts
 * @task-id M-TSK-01.SUB-02.1.MOD-02
 * @component cleanup-utilities
 * @reference foundation-context.CLEANUP-COORDINATION.003
 * @template modular-cleanup-utilities
 * @tier T1
 * @context cleanup-coordination-context
 * @category Cleanup-Modules
 * @created 2025-07-28 16:00:00 +03
 * @modified 2025-07-28 16:00:00 +03
 *
 * @description
 * Cleanup utilities module providing:
 * - Consolidated helper functions for cleanup operations
 * - Validation helpers with comprehensive rule checking
 * - Performance optimization utilities for cleanup workflows
 * - Jest compatibility utilities for testing environments
 * - Memory-safe utility operations with automatic resource management
 * - Coordination layer for extracted utility modules
 * - Integration with CleanupTemplateManager for template operations
 * - Enterprise-grade utility functions with <2ms execution overhead
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-003-cleanup-utilities-architecture
 * @governance-dcr DCR-foundation-003-cleanup-utilities-development
 * @governance-status approved
 * @governance-compliance authority-validated
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/base/MemorySafeResourceManager
 * @depends-on shared/src/base/modules/cleanup/UtilityValidation
 * @enables shared/src/base/CleanupCoordinatorEnhanced
 * @enables shared/src/base/modules/cleanup/CleanupTemplateManager
 * @related-contexts cleanup-coordination-context, foundation-context
 * @governance-impact framework-foundation, cleanup-management, utility-functions
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type cleanup-modules
 * @lifecycle-stage implementation
 * @testing-status comprehensive-test-coverage
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/cleanup-coordination-context/modules/CleanupUtilities.md
 *
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   anti-simplification-compliant: true
 *
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-07-28) - Initial cleanup utilities implementation with validation helpers
 * v1.1.0 (2025-07-28) - Added performance optimization and Jest compatibility utilities
 */

import { CleanupPriority, CleanupOperationType } from '../../CleanupCoordinatorEnhanced';
import {
  ICleanupTemplate,
  ICleanupTemplateStep,
  IValidationResult,
  ITemplateExecutionContext,
  IEnhancedCleanupConfig
} from '../../types/CleanupTypes';
import { DEFAULT_TEMPLATE_CONSTANTS } from './CleanupConfiguration';

// Import from extracted modules
import { 
  validateTemplate,
  evaluateStepCondition,
  validateConfigurationCompleteness,
  ValidationUtils
} from './UtilityValidation';

import {
  generateExecutionId,
  generateCheckpointId,
  findMatchingComponents,
  estimateOperationDuration,
  sortOperationsByDependencies,
  ExecutionUtils
} from './UtilityExecution';

import {
  generateDependencyCacheKey,
  identifyOptimizationOpportunities,
  generateMitigationStrategies,
  generateContingencyPlans,
  AnalysisUtils
} from './UtilityAnalysis';

import {
  calculateChecksum,
  deepClone,
  getNestedProperty,
  formatDuration,
  sanitizeForLogging,
  PerformanceUtils
} from './UtilityPerformance';

// ============================================================================
// TEMPLATE UTILITIES (CORE COORDINATION FUNCTIONS)
// ============================================================================

/**
 * Create default template step
 */
export function createDefaultTemplateStep(
  id: string,
  operationName: string,
  componentPattern: string = '.*'
): ICleanupTemplateStep {
  return {
    id,
    type: CleanupOperationType.RESOURCE_CLEANUP,
    componentPattern,
    operationName,
    parameters: {},
    timeout: DEFAULT_TEMPLATE_CONSTANTS.DEFAULT_STEP_TIMEOUT,
    retryPolicy: {
      maxRetries: 3,
      retryDelay: 1000,
      backoffMultiplier: 2.0,
      maxRetryDelay: 10000,
      retryOnErrors: ['TimeoutError', 'ResourceBusyError']
    },
    dependsOn: [],
    priority: DEFAULT_TEMPLATE_CONSTANTS.DEFAULT_PRIORITY,
    estimatedDuration: 5000,
    description: `Execute ${operationName} on matching components`
  };
}

/**
 * Merge template execution contexts
 */
export function mergeExecutionContexts(
  base: ITemplateExecutionContext,
  updates: Partial<ITemplateExecutionContext>
): ITemplateExecutionContext {
  return {
    ...base,
    ...updates,
    parameters: { ...base.parameters, ...updates.parameters },
    systemState: { ...base.systemState, ...updates.systemState }
  };
}

// ============================================================================
// RE-EXPORTED FUNCTIONS FOR BACKWARD COMPATIBILITY
// ============================================================================

// Template validation functions
export {
  validateTemplate,
  evaluateStepCondition,
  validateConfigurationCompleteness
};

// Execution functions
export {
  generateExecutionId,
  generateCheckpointId,
  findMatchingComponents,
  estimateOperationDuration,
  sortOperationsByDependencies
};

// Analysis functions
export {
  generateDependencyCacheKey,
  identifyOptimizationOpportunities,
  generateMitigationStrategies,
  generateContingencyPlans
};

// Performance functions
export {
  calculateChecksum,
  deepClone,
  getNestedProperty,
  formatDuration,
  sanitizeForLogging
};

// ============================================================================
// EXPORT UTILITY COLLECTIONS
// ============================================================================

/**
 * Collection of template utilities
 */
export const TemplateUtils = {
  createDefaultTemplateStep,
  mergeExecutionContexts
};

// Re-export utility collections from extracted modules
export { ValidationUtils } from './UtilityValidation';
export { ExecutionUtils } from './UtilityExecution';
export { AnalysisUtils } from './UtilityAnalysis';
export { PerformanceUtils } from './UtilityPerformance'; 