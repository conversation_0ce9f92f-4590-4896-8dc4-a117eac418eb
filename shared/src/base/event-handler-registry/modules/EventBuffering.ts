/**
 * ============================================================================
 * AI CONTEXT: Event Buffering - Event Queuing & Batch Processing
 * Purpose: Event buffering and queuing with resilient timing integration
 * Complexity: High - Complex buffering strategies with enterprise-grade timing
 * AI Navigation: 5 logical sections - Core, Buffering, Flushing, Management, Metrics
 * Dependencies: ResilientTiming, ResilientMetrics, EventTypes, AtomicCircularBuffer
 * Performance: <3ms per buffering operation
 * ============================================================================
 */

/**
 * @file Event Buffering
 * @filepath shared/src/base/event-handler-registry/modules/EventBuffering.ts
 * @task-id M-TSK-01.SUB-01.5.ENH-02.DAY-16
 * @component event-handler-registry-enhanced
 * @reference foundation-context.MEMORY-SAFETY.003
 * @template enhanced-buffering-processing
 * @tier T0
 * @context foundation-context
 * @category Memory-Safety-Enhanced-Buffering
 * @created 2025-07-27 19:15:00 +03
 * @modified 2025-07-27 19:15:00 +03
 *
 * @description
 * Event buffering system for EventHandlerRegistryEnhanced:
 * - Event buffering and queuing with configurable strategies
 * - Batch processing with enterprise-grade performance monitoring
 * - Overflow handling and buffer management with resilient timing
 * - Anti-Simplification Policy compliance with comprehensive buffering coverage
 */

import { MemorySafeResourceManager } from '../../MemorySafeResourceManager';
import { ResilientTimer, IResilientTimingResult } from '../../utils/ResilientTiming';
import { ResilientMetricsCollector } from '../../utils/ResilientMetrics';
import { AtomicCircularBufferEnhanced } from '../../AtomicCircularBufferEnhanced';
import { 
  IEventBuffering, 
  IBufferedEvent,
  IEmissionOptions
} from '../types/EventTypes';

// ============================================================================
// SECTION 1: EVENT BUFFERING CLASS (Lines 1-100)
// AI Context: "Core event buffering system with resilient timing integration"
// ============================================================================

export interface IEventBufferingConfig {
  bufferSize?: number;
  flushIntervalMs?: number;
  maxFlushSize?: number;
  enableTiming?: boolean;
  overflowStrategy?: 'drop' | 'flush' | 'expand';
}

export interface IBufferingResult {
  buffered: boolean;
  bufferSize: number;
  flushed: boolean;
  timing: IResilientTimingResult;
}

export class EventBuffering extends MemorySafeResourceManager {
  // ✅ RESILIENT TIMING: Infrastructure for enterprise-grade timing
  private _resilientTimer!: ResilientTimer;
  private _metricsCollector!: ResilientMetricsCollector;

  // Buffering infrastructure
  private _eventBuffer!: AtomicCircularBufferEnhanced<IBufferedEvent>;
  private readonly _config: Required<IEventBufferingConfig>;
  private _flushTimerId?: string;

  // Buffering metrics
  private _bufferingMetrics = {
    totalBuffered: 0,
    totalFlushed: 0,
    bufferOverflows: 0,
    flushOperations: 0,
    averageBufferTime: 0,
    averageFlushTime: 0,
    currentBufferSize: 0
  };

  constructor(config: IEventBufferingConfig = {}) {
    super({
      maxIntervals: 2,
      maxTimeouts: 3,
      maxCacheSize: (config.bufferSize || 1000) * 1024, // Estimate 1KB per event
      memoryThresholdMB: 20,
      cleanupIntervalMs: 300000 // 5 minutes
    });

    this._config = {
      bufferSize: config.bufferSize || 1000,
      flushIntervalMs: config.flushIntervalMs || 5000, // 5 seconds
      maxFlushSize: config.maxFlushSize || 100,
      enableTiming: config.enableTiming ?? true,
      overflowStrategy: config.overflowStrategy || 'flush'
    };
  }

  protected async doInitialize(): Promise<void> {
    // ✅ RESILIENT TIMING: Initialize timing infrastructure
    this._resilientTimer = new ResilientTimer({
      enableFallbacks: true,
      maxExpectedDuration: 10000, // 10 seconds max for buffering operations
      unreliableThreshold: 3,
      estimateBaseline: 3 // 3ms baseline for buffering
    });

    this._metricsCollector = new ResilientMetricsCollector({
      enableFallbacks: true,
      cacheUnreliableValues: false,
      maxMetricsAge: 300000, // 5 minutes
      defaultEstimates: new Map([
        ['bufferOperation', 3],
        ['flushOperation', 10],
        ['overflowHandling', 5]
      ])
    });

    // Initialize event buffer
    this._eventBuffer = new AtomicCircularBufferEnhanced<IBufferedEvent>(
      this._config.bufferSize
    );
    await this._eventBuffer.initialize();

    // Setup periodic flush
    this._flushTimerId = this.createSafeInterval(
      () => this._performPeriodicFlush(),
      this._config.flushIntervalMs,
      'buffer-flush'
    );
  }

  protected async doShutdown(): Promise<void> {
    // Flush remaining events
    await this._flushAllEvents();

    // Shutdown buffer
    if (this._eventBuffer) {
      await this._eventBuffer.shutdown();
    }

    // Reset metrics
    this._bufferingMetrics = {
      totalBuffered: 0,
      totalFlushed: 0,
      bufferOverflows: 0,
      flushOperations: 0,
      averageBufferTime: 0,
      averageFlushTime: 0,
      currentBufferSize: 0
    };
  }

  // ============================================================================
  // SECTION 2: EVENT BUFFERING (Lines 101-200)
  // AI Context: "Core event buffering with resilient timing measurement"
  // ============================================================================

  /**
   * ✅ RESILIENT TIMING: Buffer event with timing measurement
   */
  public async bufferEvent(
    eventType: string,
    data: unknown,
    options: IEmissionOptions = {}
  ): Promise<IBufferingResult> {
    const bufferContext = this._resilientTimer.start();
    
    try {
      this._bufferingMetrics.totalBuffered++;

      const bufferId = this._generateBufferId();
      const bufferedEvent: IBufferedEvent = {
        id: bufferId,
        type: eventType,
        data,
        options,
        timestamp: new Date(),
        priority: options.priority === 'critical' ? 4 : options.priority === 'high' ? 3 : options.priority === 'normal' ? 2 : 1,
        retryCount: 0,
        metadata: undefined,
        expectedExecutionTime: 10,
        timingRequirements: {
          maxDuration: 30000,
          requireReliableTiming: true,
          fallbackAcceptable: true
        }
      };

      // Check buffer capacity
      const currentSize = this._eventBuffer.getSize();
      if (currentSize >= this._config.bufferSize) {
        await this._handleBufferOverflow();
      }

      // Add to buffer
      await this._eventBuffer.addItem(bufferId, bufferedEvent);
      const added = true; // addItem doesn't return boolean
      
      const timing = bufferContext.end();
      this._metricsCollector.recordTiming('bufferOperation', timing);
      
      // Update metrics
      this._updateBufferingMetrics(timing.duration);
      this._bufferingMetrics.currentBufferSize = this._eventBuffer.getSize();

      return {
        buffered: added,
        bufferSize: this._eventBuffer.getSize(),
        flushed: false,
        timing
      };
    } catch (error) {
      const timing = bufferContext.end();
      this._metricsCollector.recordTiming('bufferError', timing);
      throw error;
    }
  }

  /**
   * ✅ RESILIENT TIMING: Flush buffered events with timing measurement
   */
  public async flushEvents(maxEvents?: number): Promise<IBufferedEvent[]> {
    const flushContext = this._resilientTimer.start();
    
    try {
      this._bufferingMetrics.flushOperations++;

      const eventsToFlush = maxEvents || this._config.maxFlushSize;
      const flushedEvents: IBufferedEvent[] = [];

      // Get all items and remove the first N
      const allItems = this._eventBuffer.getAllItems();
      const itemKeys = Array.from(allItems.keys()).slice(0, eventsToFlush);

      for (const key of itemKeys) {
        const event = allItems.get(key);
        if (event) {
          flushedEvents.push(event);
          await this._eventBuffer.removeItem(key);
        }
      }

      const timing = flushContext.end();
      this._metricsCollector.recordTiming('flushOperation', timing);

      // Update metrics
      this._updateFlushMetrics(timing.duration, flushedEvents.length);
      this._bufferingMetrics.currentBufferSize = this._eventBuffer.getSize();
      
      return flushedEvents;
    } catch (error) {
      const timing = flushContext.end();
      this._metricsCollector.recordTiming('flushError', timing);
      throw error;
    }
  }

  /**
   * Get current buffer size
   */
  public getBufferSize(): number {
    return this._eventBuffer.getSize();
  }

  /**
   * Check if buffer is full
   */
  public isBufferFull(): boolean {
    return this._eventBuffer.getSize() >= this._config.bufferSize;
  }

  /**
   * Check if buffer is empty
   */
  public isBufferEmpty(): boolean {
    return this._eventBuffer.getSize() === 0;
  }

  // ============================================================================
  // SECTION 3: BUFFER MANAGEMENT (Lines 201-250)
  // AI Context: "Buffer overflow handling and management"
  // ============================================================================

  private async _handleBufferOverflow(): Promise<void> {
    this._bufferingMetrics.bufferOverflows++;

    switch (this._config.overflowStrategy) {
      case 'drop':
        // Drop oldest events (handled by circular buffer)
        break;
      case 'flush':
        // Flush some events to make room
        await this.flushEvents(this._config.maxFlushSize);
        break;
      case 'expand':
        // Could expand buffer size (not implemented for safety)
        break;
    }
  }

  private async _performPeriodicFlush(): Promise<void> {
    if (!this.isBufferEmpty()) {
      await this.flushEvents();
    }
  }

  private async _flushAllEvents(): Promise<void> {
    while (!this.isBufferEmpty()) {
      await this.flushEvents();
    }
  }

  private _generateBufferId(): string {
    return `buf_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  }

  // ============================================================================
  // SECTION 4: METRICS AND MONITORING (Lines 251-300)
  // AI Context: "Buffering metrics and performance monitoring"
  // ============================================================================

  /**
   * Get buffering metrics
   */
  public getBufferingMetrics() {
    return {
      ...this._bufferingMetrics,
      bufferCapacity: this._config.bufferSize,
      bufferUtilization: this._bufferingMetrics.currentBufferSize / this._config.bufferSize,
      metricsSnapshot: this._metricsCollector.createSnapshot()
    };
  }

  /**
   * Reset buffering metrics
   */
  public resetBufferingMetrics(): void {
    this._bufferingMetrics = {
      totalBuffered: 0,
      totalFlushed: 0,
      bufferOverflows: 0,
      flushOperations: 0,
      averageBufferTime: 0,
      averageFlushTime: 0,
      currentBufferSize: this._eventBuffer.getSize()
    };
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  private _updateBufferingMetrics(duration: number): void {
    const totalBuffered = this._bufferingMetrics.totalBuffered;
    const currentAverage = this._bufferingMetrics.averageBufferTime;
    
    // Update rolling average
    this._bufferingMetrics.averageBufferTime = 
      (currentAverage * (totalBuffered - 1) + duration) / totalBuffered;
  }

  private _updateFlushMetrics(duration: number, eventsFlushed: number): void {
    const totalFlushOps = this._bufferingMetrics.flushOperations;
    const currentAverage = this._bufferingMetrics.averageFlushTime;
    
    // Update rolling average
    this._bufferingMetrics.averageFlushTime = 
      (currentAverage * (totalFlushOps - 1) + duration) / totalFlushOps;
      
    this._bufferingMetrics.totalFlushed += eventsFlushed;
  }
}
