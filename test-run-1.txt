oa-prod$ npx jest shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts --verbose --coverage --dete
ctOpenHandles
  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts (7.261 s, 584 MB heap size)
  TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing
    Resilient Timing Integration
      ✕ should use resilient timing for all operations (7 ms)
      ✕ should record timing metrics for scheduling operations (7 ms)
      ✕ should validate performance requirements with resilient timing (2 ms)
      ✕ should handle unreliable timing gracefully (2 ms)
    Timer Pool Management
      ✕ should create timer pools with comprehensive configuration (2 ms)
      ✕ should prevent duplicate pool creation (2 ms)
      ✕ should create pooled timers with strategy selection (2 ms)
      ✕ should handle pool exhaustion strategies (2 ms)
      ✕ should provide comprehensive pool statistics (2 ms)
      ✕ should remove timers from pools properly (2 ms)
    Advanced Scheduling
      ✕ should schedule recurring timers with comprehensive configuration (3 ms)
      ✕ should schedule cron timers with validation (2 ms)
      ✕ should validate cron expressions properly (14 ms)
      ✕ should schedule conditional timers with proper execution (2 ms)
      ✕ should schedule delayed timers with precise timing (1 ms)
      ✕ should schedule priority timers with queue management (1 ms)
      ✕ should validate scheduling preconditions (8 ms)
    Timer Coordination Patterns
      ✕ should create timer groups with comprehensive configuration (3 ms)
      ✕ should prevent duplicate group creation (2 ms)
      ✕ should validate group creation preconditions (7 ms)
      ✕ should synchronize timer groups with comprehensive results (6 ms)
      ✕ should handle synchronization of non-existent groups (5 ms)
      ✕ should create timer chains with workflow support (2 ms)
      ✕ should validate chain step preconditions (5 ms)
      ✕ should create timer barriers with coordination patterns (2 ms)
      ✕ should validate barrier creation preconditions (5 ms)
      ✕ should pause and resume timer groups (2 ms)
      ✕ should destroy timer groups with comprehensive cleanup (2 ms)
    Performance Requirements
      ✕ should meet pool operation performance requirements (<5ms) (3 ms)
      ✕ should meet scheduling performance requirements (<10ms) (2 ms)
      ✕ should meet synchronization performance requirements (<20ms) (2 ms)
    Phase Integration
      ✓ should initialize without phase integrations when disabled (2 ms)
      ✕ should handle phase integration gracefully when enabled (4 ms)
    Enterprise Error Handling
      ✕ should provide enhanced error context for all operations (1 ms)
      ✓ should classify errors appropriately (2 ms)
    Instance Management and Lifecycle
      ✓ should create independent instances (1 ms)
      ✓ should support multiple independent configurations (1 ms)
      ✕ should handle cleanup operations (2 ms)
      ✓ should work without explicit initialization (2 ms)
    Comprehensive Integration
      ✕ should demonstrate complete Phase 3 functionality (4 ms)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Resilient Timing Integration › should use resilient timing for all operations

    TypeError: Cannot read properties of undefined (reading 'createTimerPool')

      210 |
      211 |   public createTimerPool(poolId: string, config: ITimerPoolConfig): ITimerPool {
    > 212 |     return this._poolManager.createTimerPool(poolId, config);
          |                              ^
      213 |   }
      214 |
      215 |   public createPooledTimer(

      at TimerCoordinationServiceEnhanced.createTimerPool (shared/src/base/TimerCoordinationServiceEnhanced.ts:212:30)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:156:28)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Resilient Timing Integration › should record timing metrics for scheduling operations

    TypeError: Cannot read properties of undefined (reading 'scheduleRecurringTimer')

      237 |
      238 |   public scheduleRecurringTimer(config: IRecurringTimerConfig): string {
    > 239 |     return this._scheduler.scheduleRecurringTimer(config);
          |                            ^
      240 |   }
      241 |
      242 |   public scheduleCronTimer(

      at TimerCoordinationServiceEnhanced.scheduleRecurringTimer (shared/src/base/TimerCoordinationServiceEnhanced.ts:239:28)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:184:31)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Resilient Timing Integration › should validate performance requirements with resilient timing

    TypeError: Cannot read properties of undefined (reading 'createTimerPool')

      210 |
      211 |   public createTimerPool(poolId: string, config: ITimerPoolConfig): ITimerPool {
    > 212 |     return this._poolManager.createTimerPool(poolId, config);
          |                              ^
      213 |   }
      214 |
      215 |   public createPooledTimer(

      at TimerCoordinationServiceEnhanced.createTimerPool (shared/src/base/TimerCoordinationServiceEnhanced.ts:212:30)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:228:28)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Resilient Timing Integration › should handle unreliable timing gracefully

    TypeError: Cannot read properties of undefined (reading 'createTimerPool')

      210 |
      211 |   public createTimerPool(poolId: string, config: ITimerPoolConfig): ITimerPool {
    > 212 |     return this._poolManager.createTimerPool(poolId, config);
          |                              ^
      213 |   }
      214 |
      215 |   public createPooledTimer(

      at TimerCoordinationServiceEnhanced.createTimerPool (shared/src/base/TimerCoordinationServiceEnhanced.ts:212:30)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:264:28)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Timer Pool Management › should create timer pools with comprehensive configuration

    TypeError: Cannot read properties of undefined (reading 'createTimerPool')

      210 |
      211 |   public createTimerPool(poolId: string, config: ITimerPoolConfig): ITimerPool {
    > 212 |     return this._poolManager.createTimerPool(poolId, config);
          |                              ^
      213 |   }
      214 |
      215 |   public createPooledTimer(

      at TimerCoordinationServiceEnhanced.createTimerPool (shared/src/base/TimerCoordinationServiceEnhanced.ts:212:30)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:296:28)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Timer Pool Management › should prevent duplicate pool creation

    TypeError: Cannot read properties of undefined (reading 'createTimerPool')

      210 |
      211 |   public createTimerPool(poolId: string, config: ITimerPoolConfig): ITimerPool {
    > 212 |     return this._poolManager.createTimerPool(poolId, config);
          |                              ^
      213 |   }
      214 |
      215 |   public createPooledTimer(

      at TimerCoordinationServiceEnhanced.createTimerPool (shared/src/base/TimerCoordinationServiceEnhanced.ts:212:30)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:322:15)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Timer Pool Management › should create pooled timers with strategy selection

    TypeError: Cannot read properties of undefined (reading 'createTimerPool')

      210 |
      211 |   public createTimerPool(poolId: string, config: ITimerPoolConfig): ITimerPool {
    > 212 |     return this._poolManager.createTimerPool(poolId, config);
          |                              ^
      213 |   }
      214 |
      215 |   public createPooledTimer(

      at TimerCoordinationServiceEnhanced.createTimerPool (shared/src/base/TimerCoordinationServiceEnhanced.ts:212:30)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:342:15)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Timer Pool Management › should handle pool exhaustion strategies

    TypeError: Cannot read properties of undefined (reading 'createTimerPool')

      210 |
      211 |   public createTimerPool(poolId: string, config: ITimerPoolConfig): ITimerPool {
    > 212 |     return this._poolManager.createTimerPool(poolId, config);
          |                              ^
      213 |   }
      214 |
      215 |   public createPooledTimer(

      at TimerCoordinationServiceEnhanced.createTimerPool (shared/src/base/TimerCoordinationServiceEnhanced.ts:212:30)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:379:15)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Timer Pool Management › should provide comprehensive pool statistics

    TypeError: Cannot read properties of undefined (reading 'createTimerPool')

      210 |
      211 |   public createTimerPool(poolId: string, config: ITimerPoolConfig): ITimerPool {
    > 212 |     return this._poolManager.createTimerPool(poolId, config);
          |                              ^
      213 |   }
      214 |
      215 |   public createPooledTimer(

      at TimerCoordinationServiceEnhanced.createTimerPool (shared/src/base/TimerCoordinationServiceEnhanced.ts:212:30)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:403:15)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Timer Pool Management › should remove timers from pools properly

    TypeError: Cannot read properties of undefined (reading 'createTimerPool')

      210 |
      211 |   public createTimerPool(poolId: string, config: ITimerPoolConfig): ITimerPool {
    > 212 |     return this._poolManager.createTimerPool(poolId, config);
          |                              ^
      213 |   }
      214 |
      215 |   public createPooledTimer(

      at TimerCoordinationServiceEnhanced.createTimerPool (shared/src/base/TimerCoordinationServiceEnhanced.ts:212:30)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:436:15)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Advanced Scheduling › should schedule recurring timers with comprehensive configuration

    TypeError: Cannot read properties of undefined (reading 'scheduleRecurringTimer')

      237 |
      238 |   public scheduleRecurringTimer(config: IRecurringTimerConfig): string {
    > 239 |     return this._scheduler.scheduleRecurringTimer(config);
          |                            ^
      240 |   }
      241 |
      242 |   public scheduleCronTimer(

      at TimerCoordinationServiceEnhanced.scheduleRecurringTimer (shared/src/base/TimerCoordinationServiceEnhanced.ts:239:28)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:487:31)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Advanced Scheduling › should schedule cron timers with validation

    TypeError: Cannot read properties of undefined (reading 'scheduleCronTimer')

      246 |     timerId?: string
      247 |   ): string {
    > 248 |     return this._scheduler.scheduleCronTimer(cronExpression, callback, serviceId, timerId);
          |                            ^
      249 |   }
      250 |
      251 |   public scheduleConditionalTimer(

      at TimerCoordinationServiceEnhanced.scheduleCronTimer (shared/src/base/TimerCoordinationServiceEnhanced.ts:248:28)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:497:31)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Advanced Scheduling › should validate cron expressions properly

    expect(received).toThrow(expected)

    Expected pattern: /Invalid cron expression format/
    Received message: "Cannot read properties of undefined (reading 'scheduleCronTimer')"

          246 |     timerId?: string
          247 |   ): string {
        > 248 |     return this._scheduler.scheduleCronTimer(cronExpression, callback, serviceId, timerId);
              |                            ^
          249 |   }
          250 |
          251 |   public scheduleConditionalTimer(

          at TimerCoordinationServiceEnhanced.scheduleCronTimer (shared/src/base/TimerCoordinationServiceEnhanced.ts:248:28)
          at shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:510:17
          at Object.<anonymous> (node_modules/expect/build/toThrowMatchers.js:74:11)
          at Object.throwingMatcher [as toThrow] (node_modules/expect/build/index.js:320:21)
          at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:515:10)

      513 |           'test-service'
      514 |         );
    > 515 |       }).toThrow(/Invalid cron expression format/);
          |          ^
      516 |
      517 |       expect(() => {
      518 |         service.scheduleCronTimer(

      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:515:10)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Advanced Scheduling › should schedule conditional timers with proper execution

    TypeError: Cannot read properties of undefined (reading 'scheduleConditionalTimer')

      256 |     timerId?: string
      257 |   ): string {
    > 258 |     return this._scheduler.scheduleConditionalTimer(condition, callback, checkInterval, serviceId, timerId);
          |                            ^
      259 |   }
      260 |
      261 |   public scheduleDelayedTimer(

      at TimerCoordinationServiceEnhanced.scheduleConditionalTimer (shared/src/base/TimerCoordinationServiceEnhanced.ts:258:28)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:530:31)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Advanced Scheduling › should schedule delayed timers with precise timing

    TypeError: Cannot read properties of undefined (reading 'scheduleDelayedTimer')

      265 |     timerId?: string
      266 |   ): string {
    > 267 |     return this._scheduler.scheduleDelayedTimer(callback, delayMs, serviceId, timerId);
          |                            ^
      268 |   }
      269 |
      270 |   public schedulePriorityTimer(

      at TimerCoordinationServiceEnhanced.scheduleDelayedTimer (shared/src/base/TimerCoordinationServiceEnhanced.ts:267:28)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:555:31)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Advanced Scheduling › should schedule priority timers with queue management

    TypeError: Cannot read properties of undefined (reading 'schedulePriorityTimer')

      275 |     timerId?: string
      276 |   ): string {
    > 277 |     return this._scheduler.schedulePriorityTimer(callback, priority, intervalMs, serviceId, timerId);
          |                            ^
      278 |   }
      279 |
      280 |   // ============================================================================

      at TimerCoordinationServiceEnhanced.schedulePriorityTimer (shared/src/base/TimerCoordinationServiceEnhanced.ts:277:28)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:576:40)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Advanced Scheduling › should validate scheduling preconditions

    expect(received).toThrow(expected)

    Expected pattern: /Condition must be a function/
    Received message: "Cannot read properties of undefined (reading 'scheduleConditionalTimer')"

          256 |     timerId?: string
          257 |   ): string {
        > 258 |     return this._scheduler.scheduleConditionalTimer(condition, callback, checkInterval, serviceId, timerId);
              |                            ^
          259 |   }
          260 |
          261 |   public scheduleDelayedTimer(

          at TimerCoordinationServiceEnhanced.scheduleConditionalTimer (shared/src/base/TimerCoordinationServiceEnhanced.ts:258:28)
          at shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:604:17
          at Object.<anonymous> (node_modules/expect/build/toThrowMatchers.js:74:11)
          at Object.throwingMatcher [as toThrow] (node_modules/expect/build/index.js:320:21)
          at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:610:10)

      608 |           'test-service'
      609 |         );
    > 610 |       }).toThrow(/Condition must be a function/);
          |          ^
      611 |
      612 |       expect(() => {
      613 |         service.scheduleConditionalTimer(

      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:610:10)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Timer Coordination Patterns › should create timer groups with comprehensive configuration

    TypeError: Cannot read properties of undefined (reading 'createTimerGroup')

      288 |     coordinationType?: 'parallel' | 'sequential' | 'conditional'
      289 |   ): ITimerGroup {
    > 290 |     return this._coordinator.createTimerGroup(groupId, timerIds, coordinationType);
          |                              ^
      291 |   }
      292 |
      293 |   public async synchronizeTimerGroup(groupId: string): Promise<ISynchronizationResult> {

      at TimerCoordinationServiceEnhanced.createTimerGroup (shared/src/base/TimerCoordinationServiceEnhanced.ts:290:30)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:659:29)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Timer Coordination Patterns › should prevent duplicate group creation

    TypeError: Cannot read properties of undefined (reading 'createTimerGroup')

      288 |     coordinationType?: 'parallel' | 'sequential' | 'conditional'
      289 |   ): ITimerGroup {
    > 290 |     return this._coordinator.createTimerGroup(groupId, timerIds, coordinationType);
          |                              ^
      291 |   }
      292 |
      293 |   public async synchronizeTimerGroup(groupId: string): Promise<ISynchronizationResult> {

      at TimerCoordinationServiceEnhanced.createTimerGroup (shared/src/base/TimerCoordinationServiceEnhanced.ts:290:30)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:678:15)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Timer Coordination Patterns › should validate group creation preconditions

    expect(received).toThrow(expected)

    Expected pattern: /Invalid group ID/
    Received message: "Cannot read properties of undefined (reading 'createTimerGroup')"

          288 |     coordinationType?: 'parallel' | 'sequential' | 'conditional'
          289 |   ): ITimerGroup {
        > 290 |     return this._coordinator.createTimerGroup(groupId, timerIds, coordinationType);
              |                              ^
          291 |   }
          292 |
          293 |   public async synchronizeTimerGroup(groupId: string): Promise<ISynchronizationResult> {

          at TimerCoordinationServiceEnhanced.createTimerGroup (shared/src/base/TimerCoordinationServiceEnhanced.ts:290:30)
          at shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:687:17
          at Object.<anonymous> (node_modules/expect/build/toThrowMatchers.js:74:11)
          at Object.throwingMatcher [as toThrow] (node_modules/expect/build/index.js:320:21)
          at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:688:10)

      686 |       expect(() => {
      687 |         service.createTimerGroup('', [timer1]);
    > 688 |       }).toThrow(/Invalid group ID/);
          |          ^
      689 |
      690 |       expect(() => {
      691 |         service.createTimerGroup('test-group', []);

      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:688:10)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Timer Coordination Patterns › should synchronize timer groups with comprehensive results

    TypeError: Cannot read properties of undefined (reading 'createTimerGroup')

      288 |     coordinationType?: 'parallel' | 'sequential' | 'conditional'
      289 |   ): ITimerGroup {
    > 290 |     return this._coordinator.createTimerGroup(groupId, timerIds, coordinationType);
          |                              ^
      291 |   }
      292 |
      293 |   public async synchronizeTimerGroup(groupId: string): Promise<ISynchronizationResult> {

      at TimerCoordinationServiceEnhanced.createTimerGroup (shared/src/base/TimerCoordinationServiceEnhanced.ts:290:30)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:702:29)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Timer Coordination Patterns › should handle synchronization of non-existent groups

    expect(received).rejects.toThrow(expected)

    Expected substring: "Timer group non-existent-group not found"
    Received message:   "Cannot read properties of undefined (reading 'synchronizeTimerGroup')"

          292 |
          293 |   public async synchronizeTimerGroup(groupId: string): Promise<ISynchronizationResult> {
        > 294 |     return this._coordinator.synchronizeTimerGroup(groupId);
              |                              ^
          295 |   }
          296 |
          297 |   public createTimerChain(steps: ITimerChainStep[]): string {

          at TimerCoordinationServiceEnhanced.synchronizeTimerGroup (shared/src/base/TimerCoordinationServiceEnhanced.ts:294:30)
          at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:718:28)

      717 |     it('should handle synchronization of non-existent groups', async () => {
      718 |       await expect(service.synchronizeTimerGroup('non-existent-group'))
    > 719 |         .rejects.toThrow('Timer group non-existent-group not found');
          |                  ^
      720 |     });
      721 |
      722 |     it('should create timer chains with workflow support', () => {

      at Object.toThrow (node_modules/expect/build/index.js:218:22)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:719:18)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Timer Coordination Patterns › should create timer chains with workflow support

    TypeError: Cannot read properties of undefined (reading 'createTimerChain')

      296 |
      297 |   public createTimerChain(steps: ITimerChainStep[]): string {
    > 298 |     return this._coordinator.createTimerChain(steps);
          |                              ^
      299 |   }
      300 |
      301 |   public createTimerBarrier(

      at TimerCoordinationServiceEnhanced.createTimerChain (shared/src/base/TimerCoordinationServiceEnhanced.ts:298:30)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:742:31)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Timer Coordination Patterns › should validate chain step preconditions

    expect(received).toThrow(expected)

    Expected pattern: /Chain steps cannot be empty/
    Received message: "Cannot read properties of undefined (reading 'createTimerChain')"

          296 |
          297 |   public createTimerChain(steps: ITimerChainStep[]): string {
        > 298 |     return this._coordinator.createTimerChain(steps);
              |                              ^
          299 |   }
          300 |
          301 |   public createTimerBarrier(

          at TimerCoordinationServiceEnhanced.createTimerChain (shared/src/base/TimerCoordinationServiceEnhanced.ts:298:30)
          at shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:751:17
          at Object.<anonymous> (node_modules/expect/build/toThrowMatchers.js:74:11)
          at Object.throwingMatcher [as toThrow] (node_modules/expect/build/index.js:320:21)
          at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:752:10)

      750 |       expect(() => {
      751 |         service.createTimerChain([]);
    > 752 |       }).toThrow(/Chain steps cannot be empty/);
          |          ^
      753 |
      754 |       expect(() => {
      755 |         service.createTimerChain([

      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:752:10)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Timer Coordination Patterns › should create timer barriers with coordination patterns

    TypeError: Cannot read properties of undefined (reading 'createTimerBarrier')

      304 |     barrierType?: 'all' | 'any' | 'majority'
      305 |   ): string {
    > 306 |     return this._coordinator.createTimerBarrier(timers, callback, barrierType);
          |                              ^
      307 |   }
      308 |
      309 |   public async pauseTimerGroup(groupId: string): Promise<void> {

      at TimerCoordinationServiceEnhanced.createTimerBarrier (shared/src/base/TimerCoordinationServiceEnhanced.ts:306:30)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:790:33)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Timer Coordination Patterns › should validate barrier creation preconditions

    expect(received).toThrow(expected)

    Expected pattern: /Barrier timers cannot be empty/
    Received message: "Cannot read properties of undefined (reading 'createTimerBarrier')"

          304 |     barrierType?: 'all' | 'any' | 'majority'
          305 |   ): string {
        > 306 |     return this._coordinator.createTimerBarrier(timers, callback, barrierType);
              |                              ^
          307 |   }
          308 |
          309 |   public async pauseTimerGroup(groupId: string): Promise<void> {

          at TimerCoordinationServiceEnhanced.createTimerBarrier (shared/src/base/TimerCoordinationServiceEnhanced.ts:306:30)
          at shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:803:17
          at Object.<anonymous> (node_modules/expect/build/toThrowMatchers.js:74:11)
          at Object.throwingMatcher [as toThrow] (node_modules/expect/build/index.js:320:21)
          at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:804:10)

      802 |       expect(() => {
      803 |         service.createTimerBarrier([], () => {}, 'all');
    > 804 |       }).toThrow(/Barrier timers cannot be empty/);
          |          ^
      805 |
      806 |       expect(() => {
      807 |         service.createTimerBarrier([timer1], () => {}, 'invalid' as any);

      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:804:10)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Timer Coordination Patterns › should pause and resume timer groups

    TypeError: Cannot read properties of undefined (reading 'createTimerGroup')

      288 |     coordinationType?: 'parallel' | 'sequential' | 'conditional'
      289 |   ): ITimerGroup {
    > 290 |     return this._coordinator.createTimerGroup(groupId, timerIds, coordinationType);
          |                              ^
      291 |   }
      292 |
      293 |   public async synchronizeTimerGroup(groupId: string): Promise<ISynchronizationResult> {

      at TimerCoordinationServiceEnhanced.createTimerGroup (shared/src/base/TimerCoordinationServiceEnhanced.ts:290:30)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:812:29)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Timer Coordination Patterns › should destroy timer groups with comprehensive cleanup

    TypeError: Cannot read properties of undefined (reading 'createTimerGroup')

      288 |     coordinationType?: 'parallel' | 'sequential' | 'conditional'
      289 |   ): ITimerGroup {
    > 290 |     return this._coordinator.createTimerGroup(groupId, timerIds, coordinationType);
          |                              ^
      291 |   }
      292 |
      293 |   public async synchronizeTimerGroup(groupId: string): Promise<ISynchronizationResult> {

      at TimerCoordinationServiceEnhanced.createTimerGroup (shared/src/base/TimerCoordinationServiceEnhanced.ts:290:30)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:824:29)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Performance Requirements › should meet pool operation performance requirements (<5ms)

    TypeError: Cannot read properties of undefined (reading 'createTimerPool')

      210 |
      211 |   public createTimerPool(poolId: string, config: ITimerPoolConfig): ITimerPool {
    > 212 |     return this._poolManager.createTimerPool(poolId, config);
          |                              ^
      213 |   }
      214 |
      215 |   public createPooledTimer(

      at TimerCoordinationServiceEnhanced.createTimerPool (shared/src/base/TimerCoordinationServiceEnhanced.ts:212:30)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:857:15)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Performance Requirements › should meet scheduling performance requirements (<10ms)

    TypeError: Cannot read properties of undefined (reading 'scheduleRecurringTimer')

      237 |
      238 |   public scheduleRecurringTimer(config: IRecurringTimerConfig): string {
    > 239 |     return this._scheduler.scheduleRecurringTimer(config);
          |                            ^
      240 |   }
      241 |
      242 |   public scheduleCronTimer(

      at TimerCoordinationServiceEnhanced.scheduleRecurringTimer (shared/src/base/TimerCoordinationServiceEnhanced.ts:239:28)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:867:15)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Performance Requirements › should meet synchronization performance requirements (<20ms)

    TypeError: Cannot read properties of undefined (reading 'createTimerGroup')

      288 |     coordinationType?: 'parallel' | 'sequential' | 'conditional'
      289 |   ): ITimerGroup {
    > 290 |     return this._coordinator.createTimerGroup(groupId, timerIds, coordinationType);
          |                              ^
      291 |   }
      292 |
      293 |   public async synchronizeTimerGroup(groupId: string): Promise<ISynchronizationResult> {

      at TimerCoordinationServiceEnhanced.createTimerGroup (shared/src/base/TimerCoordinationServiceEnhanced.ts:290:30)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:881:29)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Phase Integration › should handle phase integration gracefully when enabled

    expect(received).not.toThrow()

    Error name:    "TypeError"
    Error message: "Cannot read properties of undefined (reading 'isPhase1Enabled')"

          392 |   // Phase integration methods
          393 |   public isPhase1Enabled(): boolean {
        > 394 |     return this._phaseIntegration.isPhase1Enabled();
              |                                   ^
          395 |   }
          396 |
          397 |   public isPhase2Enabled(): boolean {

          at TimerCoordinationServiceEnhanced.isPhase1Enabled (shared/src/base/TimerCoordinationServiceEnhanced.ts:394:35)
          at shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:915:38
          at Object.<anonymous> (node_modules/expect/build/toThrowMatchers.js:74:11)
          at Object.throwingMatcher [as toThrow] (node_modules/expect/build/index.js:320:21)
          at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:915:61)

      913 |
      914 |       // Should work without throwing (auto-initialization)
    > 915 |       expect(() => integratedService.isPhase1Enabled()).not.toThrow();
          |                                                             ^
      916 |       expect(() => integratedService.isPhase2Enabled()).not.toThrow();
      917 |     });
      918 |   });

      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:915:61)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Enterprise Error Handling › should provide enhanced error context for all operations

    expect(received).toBeDefined()

    Received: undefined

      929 |       } catch (error: any) {
      930 |         expect(error).toBeInstanceOf(Error);
    > 931 |         expect(error.operationId).toBeDefined();
          |                                   ^
      932 |         expect(error.context).toBeDefined();
      933 |         expect(error.timestamp).toBeDefined();
      934 |         expect(error.component).toBe('TimerCoordinationServiceEnhanced');

      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:931:35)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Instance Management and Lifecycle › should handle cleanup operations

    TypeError: Cannot read properties of undefined (reading 'createTimerPool')

      210 |
      211 |   public createTimerPool(poolId: string, config: ITimerPoolConfig): ITimerPool {
    > 212 |     return this._poolManager.createTimerPool(poolId, config);
          |                              ^
      213 |   }
      214 |
      215 |   public createPooledTimer(

      at TimerCoordinationServiceEnhanced.createTimerPool (shared/src/base/TimerCoordinationServiceEnhanced.ts:212:30)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:996:15)

  ● TimerCoordinationServiceEnhanced - Enhanced with Resilient Timing › Comprehensive Integration › should demonstrate complete Phase 3 functionality

    TypeError: Cannot read properties of undefined (reading 'createTimerPool')

      210 |
      211 |   public createTimerPool(poolId: string, config: ITimerPoolConfig): ITimerPool {
    > 212 |     return this._poolManager.createTimerPool(poolId, config);
          |                              ^
      213 |   }
      214 |
      215 |   public createPooledTimer(

      at TimerCoordinationServiceEnhanced.createTimerPool (shared/src/base/TimerCoordinationServiceEnhanced.ts:212:30)
      at Object.<anonymous> (shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts:1041:28)

--------------------------------------|---------|----------|---------|---------|-----------------------------------------------------
File                                  | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s
--------------------------------------|---------|----------|---------|---------|-----------------------------------------------------
All files                             |    6.86 |     3.36 |    6.04 |       7 |
 base                                 |    6.68 |     1.62 |    7.67 |    6.83 |
  AtomicCircularBuffer.ts             |    2.77 |        0 |       0 |    2.88 | 114-475
  AtomicCircularBufferEnhanced.ts     |    0.44 |        0 |       0 |    0.47 | 205-1346
  EventHandlerRegistry.ts             |    4.82 |        0 |       0 |    4.89 | 147-528,536-539,543-548
  EventHandlerRegistryEnhanced.ts     |    1.02 |        0 |       0 |    1.05 | 281-1977
  LoggingMixin.ts                     |   11.42 |     6.66 |   16.66 |   11.42 | 78-113,157-204
  MemorySafeResourceManager.ts        |      10 |     5.55 |    5.66 |   10.24 | 156-640,660-796,818-883,903-916,923-938,954-973
  TimerCoordinationService.ts         |      25 |     6.52 |   22.58 |   24.68 | ...,347-348,359,368-392,403-586,608-617,638-664,684
  TimerCoordinationServiceEnhanced.ts |    30.1 |        0 |   38.46 |    30.1 | ...,222-230,310-359,368-372,381-389,398-402,416-435
 base/timer-coordination/modules      |    6.06 |     8.09 |    1.73 |    6.16 |
  AdvancedScheduler.ts                |    2.65 |        0 |       0 |     2.7 | 72-677
  PhaseIntegration.ts                 |    6.45 |        0 |       0 |    6.45 | 93-364
  TimerConfiguration.ts               |   60.41 |    48.57 |   33.33 |   60.41 | 193,197,201,207,211,218,222,273,280,287-295,309-317
  TimerCoordinationPatterns.ts        |    1.73 |        0 |       0 |    1.79 | 76-741
  TimerPoolManager.ts                 |     2.7 |        0 |       0 |    2.73 | 67-542
  TimerUtilities.ts                   |    3.01 |        0 |       0 |    3.03 | 67-452,464-469
 base/utils                           |   12.94 |     2.89 |    4.34 |   12.94 |
  ResilientMetrics.ts                 |   11.76 |      2.7 |    3.33 |   11.76 | 69-234,243-281,290-329,347-363,371-382
  ResilientTiming.ts                  |    14.7 |     3.12 |    6.25 |    14.7 | 52-81,95-184,197,201,212-227,234-253
--------------------------------------|---------|----------|---------|---------|-----------------------------------------------------
Test Suites: 1 failed, 1 total
Tests:       35 failed, 5 passed, 40 total
Snapshots:   0 total
Time:        8.695 s
Ran all test suites matching /shared\/src\/base\/__tests__\/TimerCoordinationServiceEnhanced.test.ts/i.